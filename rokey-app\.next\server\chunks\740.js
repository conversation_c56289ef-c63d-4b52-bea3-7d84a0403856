"use strict";exports.id=740,exports.ids=[740],exports.modules={80740:(e,t,r)=>{r.d(t,{BrowsingExecutionService:()=>i});var a=r(750);class s{constructor(){this.activePlans=new Map,this.browserlessService=a.A.getInstance()}static getInstance(){return s.instance||(s.instance=new s),s.instance}async executeSmartBrowsing(e,t,r="search",a){try{let s=await this.createBrowsingPlan(e,r,t);this.activePlans.set(s.id,s),this.logPlan(s),a?.onPlanCreated?.(s);let i=await this.executePlan(s,t,a);if(i.success)return{success:!0,content:i.content,plan:s};if(this.isNetworkError(i.error))return{success:!1,error:`Network connectivity issue: ${i.error}. Falling back to simple browsing.`,plan:s,shouldFallback:!0};return{success:!1,error:i.error,plan:s}}catch(t){let e=t instanceof Error?t.message:"Unknown error";if(this.isNetworkError(e))return{success:!1,error:`Network connectivity issue: ${e}. Falling back to simple browsing.`,shouldFallback:!0};return{success:!1,error:`Smart browsing failed: ${e}`}}}isNetworkError(e){return["fetch failed","ECONNRESET","ENOTFOUND","ETIMEDOUT","ECONNREFUSED","Network request failed","Connection timeout","DNS resolution failed"].some(t=>e.toLowerCase().includes(t.toLowerCase()))}async createBrowsingPlan(e,t,r){let a=`plan_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,s=await this.generatePlanWithAI(e,t,r);return{id:a,originalQuery:e,goal:s.goal||`Find comprehensive information about: ${e}`,subtasks:s.subtasks||this.createFallbackPlan(e,t),status:"planning",progress:0,gatheredData:{},visitedUrls:[],searchQueries:[],completedSubtasks:[],failedSubtasks:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}}async generatePlanWithAI(e,t,r){try{let a=r.browsing_models[0];if(!a)throw Error("No browsing models available for planning");let s=this.buildPlanningPrompt(e,t),i=await this.callAIForPlanning(s,a);if(i.success&&i.content)return this.parsePlanFromAI(i.content,e);return{goal:`Find information about: ${e}`,subtasks:this.createFallbackPlan(e,t)}}catch(r){return{goal:`Find information about: ${e}`,subtasks:this.createFallbackPlan(e,t)}}}buildPlanningPrompt(e,t){let r=new Date,a=r.toLocaleString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",timeZoneName:"short"}),s=r.getFullYear(),i=r.toLocaleString("en-US",{month:"long"});return`You are an expert web browsing strategist. Create a detailed browsing plan to thoroughly research this query: "${e}"

CURRENT DATE & TIME: ${a}
CURRENT YEAR: ${s}
CURRENT MONTH: ${i}

BROWSING TYPE: ${t}

IMPORTANT: When generating search terms and subtasks, consider the current date and time context:
- For recent events, include "${s}" in search terms
- For current trends, use "latest", "recent", "${i} ${s}"
- For news queries, prioritize recent timeframes
- For technology/AI topics, include current year for latest developments
- For market/business queries, focus on current and recent data

Create a JSON response with this structure:
{
  "goal": "Clear statement of what we want to achieve",
  "subtasks": [
    {
      "id": "unique_id",
      "type": "search|navigate|extract|analyze",
      "description": "What this subtask does",
      "query": "Specific search query or URL",
      "priority": 1-10,
      "searchTerms": ["alternative", "search", "terms"],
      "expectedInfo": "What information we expect to find"
    }
  ]
}

GUIDELINES:
1. Start with broad searches, then get more specific
2. Use multiple search strategies and terms with temporal context
3. Include fact-checking and verification steps
4. Plan for 3-7 subtasks maximum
5. Make search terms diverse, comprehensive, and time-aware
6. Consider different angles and perspectives
7. Include analysis steps to synthesize information
8. For "navigate" tasks, only use if you have specific URLs (https://...)
9. Most tasks should be "search" type for better reliability
10. ALWAYS include temporal keywords when relevant:
    - For news: "latest news", "recent updates", "${i} ${s}"
    - For trends: "current trends", "${s} trends"
    - For technology: "latest developments", "${s} updates"
    - For data/statistics: "current data", "recent statistics", "${s} data"

Create a smart, thorough, and temporally-aware plan:`}enhanceQueryWithTemporal(e,t){let r=new Date().getFullYear(),a=new Date().toLocaleString("en-US",{month:"long"}),s=/\b(latest|recent|current|new|today|\d{4}|now)\b/i.test(e);return{primary:e,temporal:s?e:`${e} ${r} latest`,alternatives:[e,`${e} information`,`${e} details`,s?`${e} overview`:`${e} ${r}`],recentTerms:[`${e} recent`,`${e} latest news`,`${e} ${a} ${r}`,`${e} current trends`]}}createFallbackPlan(e,t){let r=Date.now(),a=new Date().getFullYear(),s=new Date().toLocaleString("en-US",{month:"long"}),i=[`${a}`,"latest","recent",`${s} ${a}`],n=this.enhanceQueryWithTemporal(e,i);return[{id:`search_${r}_1`,type:"search",description:"Primary search for main topic",query:n.primary,status:"pending",priority:10,attempts:0,maxAttempts:3,searchTerms:n.alternatives,expectedInfo:"General information about the topic"},{id:`search_${r}_2`,type:"search",description:"Secondary search with temporal context",query:n.temporal,status:"pending",priority:8,attempts:0,maxAttempts:3,searchTerms:n.recentTerms,expectedInfo:"Recent developments and current information"},{id:`analyze_${r}`,type:"analyze",description:"Analyze and synthesize gathered information",query:"synthesize findings",status:"pending",priority:5,attempts:0,maxAttempts:1,dependencies:[`search_${r}_1`,`search_${r}_2`],expectedInfo:"Comprehensive summary of findings"}]}async executePlan(e,t,r){e.status="executing",e.updatedAt=new Date().toISOString();try{for(;this.hasRemainingTasks(e);){let a=this.getNextExecutableTask(e);if(!a)break;e.currentSubtask=a.id,a.status="in_progress";let s=this.enhanceSubtaskQuery(a,e),i=e.subtasks.findIndex(e=>e.id===a.id);-1!==i&&(e.subtasks[i]=s),r?.onTaskStarted?.(s,e),r?.onStatusUpdate?.(`Executing: ${s.description}`,e);let n=await this.executeSubtask(s,e,t);n.success?(a.status="completed",a.result=n.data,e.completedSubtasks.push(a.id),e.gatheredData[a.id]={taskType:a.type,description:a.description,query:a.query,result:n.data,completedAt:new Date().toISOString()},r?.onTaskCompleted?.(a,e)):(a.attempts++,a.error=n.error,a.attempts>=a.maxAttempts?(a.status="failed",e.failedSubtasks.push(a.id),r?.onTaskFailed?.(a,e)):a.status="pending"),e.progress=this.calculateProgress(e),r?.onProgressUpdate?.(e.progress,e),e.updatedAt=new Date().toISOString(),this.logProgress(e)}let a=await this.synthesizeFinalResult(e,t);return e.finalResult=a,e.status="completed",e.progress=100,r?.onPlanCompleted?.(e),r?.onStatusUpdate?.("Browsing completed successfully!",e),{success:!0,content:a}}catch(t){return e.status="failed",{success:!1,error:t instanceof Error?t.message:"Unknown error"}}}hasRemainingTasks(e){return e.subtasks.some(e=>"pending"===e.status||"in_progress"===e.status)}getNextExecutableTask(e){let t=e.subtasks.filter(t=>"pending"===t.status&&(!t.dependencies||!(t.dependencies.length>0)||t.dependencies.every(t=>e.completedSubtasks.includes(t))));return t.sort((e,t)=>t.priority-e.priority),t[0]||null}async executeSubtask(e,t,r){try{switch(e.type){case"search":return await this.executeSearchSubtask(e,t);case"navigate":return await this.executeNavigateSubtask(e,t);case"extract":return await this.executeExtractSubtask(e,t);case"analyze":return await this.executeAnalyzeSubtask(e,t,r);default:throw Error(`Unknown subtask type: ${e.type}`)}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async executeSearchSubtask(e,t){let r=this.buildContextualQuery(e,t),a=[r,e.query,...e.searchTerms||[]],s="",i=null,n=0;if(this.detectComplexAutomationNeeds(e,r))return await this.executeComplexAutomationSearch(e,t,r);for(let r of a)try{t.searchQueries.push(r);let a=await this.browserlessService.searchAndExtractUnblocked(r);if(a.data&&a.data.results&&a.data.results.length>0){let r=this.calculateResultQuality(a.data.results,e.query);if(a.data.results.forEach(e=>{e.link&&!t.visitedUrls.includes(e.link)&&t.visitedUrls.push(e.link)}),r>n&&(n=r,i=a.data),r>.8)return{success:!0,data:a.data}}else s=`No results found for: "${r}"`}catch(e){s=e instanceof Error?e.message:"Search failed"}return i&&n>.3?{success:!0,data:i}:{success:!1,error:`All search terms failed to find quality results. Last error: ${s}`}}calculateResultQuality(e,t){if(!e||0===e.length)return 0;let r=0,a=t.toLowerCase().split(/\s+/).filter(e=>e.length>2),s=new Date().getFullYear().toString();for(let t of e){let e=0,i=(t.title||"").toLowerCase(),n=(t.snippet||"").toLowerCase(),o=`${i} ${n}`;n&&"no description available"!==n&&(e+=.3),e+=a.filter(e=>o.includes(e)).length/a.length*.4,o.includes(s)&&(e+=.2),e+=Math.min(.1*["latest","recent","current","new","2025","updated"].filter(e=>o.includes(e)).length,.3),n.length<50&&(e*=.7),n.length>150&&(e+=.1),r+=e}return Math.min(r/e.length,1)}async executeNavigateSubtask(e,t){try{let r=e.query;if(/^https?:\/\//i.test(r)){if(t.visitedUrls.includes(r)||t.visitedUrls.push(r),this.detectComplexAutomationNeeds(e,r))return await this.executeComplexAutomation(e,r,t);{let e=await this.browserlessService.performEnhancedWebSearch(r,{maxSites:3,timeout:45e3});if(e.success&&e.results.length>0){let t=e.results.map(e=>`Source: ${e.title} (${e.url})
${e.content}`).join("\n\n---\n\n");return{success:!0,data:t,metadata:{sitesVisited:e.results.length,totalSitesDiscovered:e.discoveredSites,searchQuery:r}}}{let e=await this.browserlessService.navigateAndExtract(r);if(e.data)return{success:!0,data:e.data};return{success:!1,error:"Both enhanced search and standard navigation failed"}}}}{let a=r;r.toLowerCase().includes("navigate to")&&(a=r.replace(/navigate to\s*/i,"").trim()),r.toLowerCase().includes("websites of")&&(a=a.replace(/websites of\s*/i,"").trim());let s=new Date().getFullYear();return a.includes(s.toString())||(a=`${a} ${s}`),await this.executeSearchSubtask({...e,type:"search",query:a,searchTerms:[a,`${a} latest`,`${a} official website`,`${a} information`]},t)}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Navigation failed"}}}detectComplexAutomationNeeds(e,t){let r=(e.description||"").toLowerCase(),a=(e.query||"").toLowerCase(),s=["earliest flight","flight from","flight to","book flight","search flight","flight booking","airline","departure","arrival","connecting flight","flight schedule","flight search","travel booking"].some(e=>r.includes(e)||a.includes(e)),i=["form","submit","login","register","book","reserve","purchase","checkout","payment","captcha","verify","authenticate","sign in","sign up","create account","fill out","application","survey","reservation","booking","order"].some(e=>r.includes(e)||a.includes(e))||["login","register","checkout","booking","reservation","form","application","survey","account","dashboard","admin","portal"].some(e=>t.toLowerCase().includes(e));return s||i}async executeComplexAutomation(e,t,r){try{let a=this.buildAutomationWorkflow(e,t);try{let e=await this.browserlessService.executeComplexWorkflow(t,a);if(e&&e.data)return e.sessionId&&e.reconnectUrl&&(r.sessionInfo={sessionId:e.sessionId,reconnectUrl:e.reconnectUrl,lastUsed:new Date}),{success:!0,data:e.data};throw Error("Complex automation completed but no data extracted")}catch(r){let t=r instanceof Error?r.message:"Complex automation failed";try{let t=this.extractSearchQueryFromSubtask(e),r=await this.browserlessService.searchAndExtractUnblocked(t);if(r.success&&r.data)return{success:!0,data:r.data};throw Error("Fallback search also failed")}catch(r){let e=r instanceof Error?r.message:"Fallback search failed";return{success:!1,error:`Complex automation failed: ${t}. Fallback search failed: ${e}`}}}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Complex automation setup failed"}}}buildAutomationWorkflow(e,t){let r=[],a=(e.description||"").toLowerCase(),s=(e.query||"").toLowerCase();if(r.push({name:"navigate",type:"navigate",params:{url:t,waitUntil:"networkIdle"}}),r.push({name:"wait_for_page",type:"wait",params:{time:3e3}}),r.push({name:"wait_for_page_ready",type:"wait",params:{time:3e3}}),a.includes("search")||s.includes("search")||a.includes("find")||a.includes("look for")){r.push({name:"find_search_input",type:"waitForSelector",params:{selector:'input[type="search"], input[name*="search"], input[id*="search"], input[placeholder*="search"], .search-input',timeout:1e4}});let e=this.extractSearchTermsFromQuery(s);e&&(r.push({name:"enter_search_terms",type:"type",params:{selector:'input[type="search"], input[name*="search"], input[id*="search"], input[placeholder*="search"], .search-input',text:e}}),r.push({name:"submit_search",type:"click",params:{selector:'button[type="submit"], button[class*="search"], .search-button, input[type="submit"]'}}),r.push({name:"wait_for_search_results",type:"wait",params:{time:3e3}}))}return(a.includes("form")||a.includes("fill")||a.includes("submit")||a.includes("input"))&&(r.push({name:"wait_for_form",type:"waitForSelector",params:{selector:"form, input, textarea, select",timeout:1e4}}),r.push({name:"analyze_form",type:"extract",params:{selector:"form, input, textarea, select",type:"html"}})),(a.includes("login")||a.includes("sign in")||a.includes("authenticate")||a.includes("credentials"))&&(r.push({name:"wait_for_login_form",type:"waitForSelector",params:{selector:'form[action*="login"], form[id*="login"], input[type="password"], .login-form',timeout:1e4}}),r.push({name:"extract_login_form",type:"extract",params:{selector:'form[action*="login"], form[id*="login"], input[type="password"], .login-form',type:"html"}})),(a.includes("product")||a.includes("buy")||a.includes("purchase")||a.includes("shop"))&&r.push({name:"extract_product_info",type:"extract",params:{selector:'.product, .item, [data-testid*="product"], .price, .title, .description',type:"html"}}),(a.includes("scroll")||a.includes("load more")||a.includes("pagination")||a.includes("infinite")||a.includes("more results"))&&(r.push({name:"scroll_content",type:"scroll",params:{selector:"body",distance:1e3}}),r.push({name:"wait_after_scroll",type:"wait",params:{time:2e3}}),r.push({name:"scroll_more",type:"scroll",params:{selector:"body",distance:1e3}}),r.push({name:"wait_after_second_scroll",type:"wait",params:{time:2e3}}),r.push({name:"click_load_more",type:"click",params:{selector:'button[class*="load"], button[class*="more"], .load-more, .show-more, [data-testid*="load"]',timeout:5e3,optional:!0}})),(a.includes("book")||a.includes("reserve")||a.includes("appointment")||a.includes("schedule"))&&(r.push({name:"wait_for_booking_interface",type:"waitForSelector",params:{selector:'.booking, .reservation, .calendar, .schedule, [class*="book"], [class*="reserve"]',timeout:1e4}}),r.push({name:"extract_booking_options",type:"extract",params:{selector:'.booking, .reservation, .calendar, .schedule, [class*="book"], [class*="reserve"]',type:"html"}})),r.push({name:"extract_main_content",type:"extract",params:{selector:"main, .main, .content, .container, body",type:"text"}}),r.push({name:"extract_structured_data",type:"extract",params:{selector:"[itemscope], [data-*], .product, .article, .post, .result, .listing",type:"html"}}),r.push({name:"extract_navigation",type:"extract",params:{selector:"nav, .nav, .navigation, .menu, a[href]",type:"html"}}),r.push({name:"take_screenshot",type:"screenshot",params:{fullPage:!1}}),r}extractSearchTermsFromQuery(e){let t=e.replace(/^(search for|find|look for|search|browse|navigate to)\s+/i,"").replace(/\s+(on|in|at|from)\s+\w+\.(com|org|net|io).*$/i,"").trim();return t.length>0?t:null}extractSearchQueryFromSubtask(e){let t=e.description;e.query&&(t=e.query),t=t.replace(/^(find|search for|look for|determine|analyze)\s+/i,"").replace(/\s+(today|now|currently|latest|recent)\s*/i," ").trim();let r=new Date().toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(t.includes("today")||t.includes("earliest")||t.includes("latest"))&&(t+=` ${r}`),t}buildFlightBookingWorkflow(e,t,r){let a=[],s=e.description.toLowerCase(),i=this.getFlightBookingContext(t);if(a.push({name:"navigate_to_booking_site",type:"navigate",params:{url:r,waitUntil:"networkIdle"}}),a.push({name:"wait_for_page_load",type:"wait",params:{time:3e3}}),a.push({name:"dismiss_popups",type:"click",params:{selector:'[data-testid="cookie-banner-close"], .cookie-banner button, .modal-close, [aria-label="Close"]',optional:!0}}),i.origin){let e=this.getOriginSelectors(r);a.push({name:"fill_origin",type:"type",params:{selector:e.join(", "),text:i.origin,clear:!0}}),a.push({name:"wait_for_origin_autocomplete",type:"wait",params:{time:1500}}),a.push({name:"select_origin",type:"click",params:{selector:'.autocomplete-item:first-child, .suggestion:first-child, li:first-child, [role="option"]:first-child',optional:!0}})}if(i.destination){let e=this.getDestinationSelectors(r);a.push({name:"fill_destination",type:"type",params:{selector:e.join(", "),text:i.destination,clear:!0}}),a.push({name:"wait_for_destination_autocomplete",type:"wait",params:{time:1500}}),a.push({name:"select_destination",type:"click",params:{selector:'.autocomplete-item:first-child, .suggestion:first-child, li:first-child, [role="option"]:first-child',optional:!0}})}let n=this.getDateSelectors(r);a.push({name:"click_departure_date",type:"click",params:{selector:n.join(", ")}}),a.push({name:"wait_for_date_picker",type:"wait",params:{time:1e3}});let o=new Date,l=`${o.getFullYear()}-${String(o.getMonth()+1).padStart(2,"0")}-${String(o.getDate()).padStart(2,"0")}`,c=[`[data-date="${l}"]`,`[aria-label*="${o.getDate()}"]`,".today",".current-day",".selected",`button:contains("${o.getDate()}")`,`td:contains("${o.getDate()}")`];a.push({name:"select_today",type:"click",params:{selector:c.join(", "),optional:!0}}),(s.includes("connecting")||s.includes("after arrival"))&&a.push({name:"select_one_way",type:"click",params:{selector:'input[value="oneway"], label[for*="oneway"], .trip-type .one-way, [data-testid*="oneway"]',optional:!0}}),a.push({name:"wait_before_submit",type:"wait",params:{time:2e3}});let u=this.getSearchButtonSelectors(r);return a.push({name:"submit_search",type:"click",params:{selector:u.join(", ")}}),a.push({name:"wait_for_results",type:"wait",params:{time:5e3}}),a.push({name:"wait_for_loading_complete",type:"waitForSelector",params:{selector:'.flight-results, .search-results, [data-testid="results"]',timeout:15e3}}),a.push({name:"extract_flight_results",type:"extract",params:{selector:'.flight-result, .flight-option, .search-result, [data-testid*="flight"]',type:"html"}}),a.push({name:"extract_flight_details",type:"extract",params:{selector:".departure-time, .arrival-time, .flight-duration, .airline-name, .price",type:"text"}}),a.push({name:"take_results_screenshot",type:"screenshot",params:{fullPage:!1}}),a}getFlightBookingContext(e){let t={origin:null,destination:null,departureTime:null,arrivalTime:null,duration:null},r=e.originalQuery.toLowerCase();for(let a of(e.goal.toLowerCase(),r.includes("owerri")&&r.includes("abuja")?(t.origin="Owerri",t.destination="Abuja"):r.includes("abuja")&&r.includes("dubai")&&(t.origin="Abuja",t.destination="Dubai"),e.subtasks.filter(e=>"completed"===e.status&&e.result))){let e=JSON.stringify(a.result).toLowerCase(),r=e.match(/(\d{1,2}:\d{2}(?:\s*(?:am|pm))?)/);r&&!t.departureTime&&(t.departureTime=r[1]);let s=e.match(/(\d+(?:\s*(?:hours?|hrs?|h))?(?:\s*(?:and|\&)?\s*\d+(?:\s*(?:minutes?|mins?|m))?)?)/);s&&!t.duration&&(t.duration=s[1])}return t}getOriginSelectors(e){let t=new URL(e).hostname.toLowerCase(),r=['input[placeholder*="From"]','input[name*="origin"]','input[id*="from"]','input[aria-label*="From"]','input[data-testid*="origin"]','input[data-testid*="from"]'];return t.includes("kayak")?['input[placeholder*="From"]','input[aria-label*="Flight origin"]','input[data-testid="origin"]',...r]:t.includes("expedia")?['input[id*="flight-origin"]','input[aria-label*="Leaving from"]','input[data-testid*="origin"]',...r]:t.includes("skyscanner")?['input[placeholder*="From"]','input[data-testid*="origin"]','input[name*="OriginPlace"]',...r]:t.includes("google")?['input[placeholder*="Where from"]','input[aria-label*="Where from"]','input[jsname*="origin"]',...r]:r}getDestinationSelectors(e){let t=new URL(e).hostname.toLowerCase(),r=['input[placeholder*="To"]','input[name*="destination"]','input[id*="to"]','input[aria-label*="To"]','input[data-testid*="destination"]','input[data-testid*="to"]'];return t.includes("kayak")?['input[placeholder*="To"]','input[aria-label*="Flight destination"]','input[data-testid="destination"]',...r]:t.includes("expedia")?['input[id*="flight-destination"]','input[aria-label*="Going to"]','input[data-testid*="destination"]',...r]:t.includes("skyscanner")?['input[placeholder*="To"]','input[data-testid*="destination"]','input[name*="DestinationPlace"]',...r]:t.includes("google")?['input[placeholder*="Where to"]','input[aria-label*="Where to"]','input[jsname*="destination"]',...r]:r}getDateSelectors(e){let t=new URL(e).hostname.toLowerCase(),r=['input[placeholder*="Departure"]','input[name*="departure"]','input[id*="depart"]','input[data-testid*="departure"]','button[data-testid*="departure"]'];return t.includes("kayak")?['input[aria-label*="Start date"]','input[data-testid="departure-date"]','button[data-testid="departure-date"]',...r]:t.includes("expedia")?['input[id*="flight-departing"]','button[data-testid*="departure"]','input[aria-label*="Departing"]',...r]:t.includes("skyscanner")?['input[placeholder*="Depart"]','button[data-testid*="depart"]','input[name*="OutboundDate"]',...r]:t.includes("google")?['input[placeholder*="Departure"]','input[aria-label*="Departure"]','div[data-value*="departure"]',...r]:r}getSearchButtonSelectors(e){let t=new URL(e).hostname.toLowerCase(),r=['button[type="submit"]',".search-button",'button:contains("Search")','[data-testid="submit"]','button[aria-label*="Search"]'];return t.includes("kayak")?['button[aria-label*="Search"]','button[data-testid="submit-button"]',".Common-Widgets-Button-ButtonPrimary",...r]:t.includes("expedia")?['button[data-testid*="search"]','button[aria-label*="Search"]',".btn-primary",...r]:t.includes("skyscanner")?['button[data-testid*="search"]','button:contains("Search flights")',".BpkButton--primary",...r]:t.includes("google")?['button[aria-label*="Search"]','button[jsname*="search"]',".VfPpkd-LgbsSe",...r]:r}async executeExtractSubtask(e,t){return this.executeNavigateSubtask(e,t)}async executeAnalyzeSubtask(e,t,r){try{let e=t.subtasks.filter(e=>"completed"===e.status&&e.result).map(e=>({type:e.type,description:e.description,query:e.query,data:e.result}));if(0===e.length)return{success:!1,error:"No data available for analysis"};let a=r.browsing_models[0];if(!a)return{success:!1,error:"No AI model available for analysis"};let s=this.buildAnalysisPrompt(t.originalQuery,e),i=await this.callAIForAnalysis(s,a);if(i.success&&i.content)return{success:!0,data:{analysis:i.content,sourceData:e}};return{success:!1,error:i.error||"Analysis failed"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Analysis failed"}}}calculateProgress(e){let t=e.subtasks.length;return Math.round(e.completedSubtasks.length/t*100)}logProgress(e){e.completedSubtasks.length,e.failedSubtasks.length,e.subtasks.length}buildContextualQuery(e,t){let r=e.query,a=t.subtasks.filter(t=>"completed"===t.status&&t.result&&t.id!==e.id);if(0===a.length)return r;let s=[];for(let e of a){let t=e.result;if(e.description.toLowerCase().includes("flight")&&e.description.toLowerCase().includes("earliest")){let e=this.extractFlightInfo(t);e&&s.push(e)}if(e.description.toLowerCase().includes("duration")){let e=this.extractDurationInfo(t);e&&s.push(e)}if(e.description.toLowerCase().includes("arrive")){let e=this.extractArrivalInfo(t);e&&s.push(e)}}if(s.length>0){let t=s.join(", ");if(e.description.toLowerCase().includes("connecting")||e.description.toLowerCase().includes("after arrival")){let a=this.extractTimeFromContext(s);if(a){let t=this.calculateConnectingFlightTime(a);r=`${e.query} departing after ${t}`}else r=`${e.query} departing after ${t}`}else if(e.description.toLowerCase().includes("duration")&&s.some(e=>e.includes("flight"))){let t=s.find(e=>e.includes("flight"));r=`${e.query} for ${t}`}else if(e.description.toLowerCase().includes("earliest")&&s.some(e=>e.includes("arrives"))){let t=s.find(e=>e.includes("arrives"));r=`${e.query} after ${t}`}else r=`${e.query} (context: ${t})`}return r}extractFlightInfo(e){if(!e)return null;try{let t=JSON.stringify(e).toLowerCase(),r=t.match(/(\d{1,2}:\d{2}(?:\s*(?:am|pm))?)/gi),a=t.match(/(arik air|air peace|dana air|azman air|emirates|qatar|turkish)/gi);if(r&&r.length>0){let e=r[0],t=a?a[0]:"";return`earliest flight ${e}${t?` on ${t}`:""}`}}catch(e){}return null}extractDurationInfo(e){if(!e)return null;try{let t=JSON.stringify(e).toLowerCase().match(/(\d+(?:\s*(?:hours?|hrs?|h))?(?:\s*(?:and|\&)?\s*\d+(?:\s*(?:minutes?|mins?|m))?)?)/gi);if(t&&t.length>0)return`duration ${t[0]}`}catch(e){}return null}extractArrivalInfo(e){if(!e)return null;try{let t=JSON.stringify(e).toLowerCase().match(/(?:arrives?|arrival|landing)(?:\s*(?:at|in|by))?\s*(\d{1,2}:\d{2}(?:\s*(?:am|pm))?)/gi);if(t&&t.length>0)return`arrives ${t[0]}`}catch(e){}return null}extractTimeFromContext(e){for(let t of e){let e=t.match(/(\d{1,2}:\d{2}(?:\s*(?:am|pm))?)/i);if(e)return e[1]}return null}calculateConnectingFlightTime(e){try{let t=e.match(/(\d{1,2}):(\d{2})(?:\s*(am|pm))?/i);if(!t)return e;let r=parseInt(t[1]),a=parseInt(t[2]),s=t[3]?.toLowerCase();"pm"===s&&12!==r?r+=12:"am"===s&&12===r&&(r=0),(r+=2)>=24&&(r-=24);let i=0===r?12:r>12?r-12:r,n=r>=12?"PM":"AM";return`${i}:${String(a).padStart(2,"0")} ${n}`}catch(t){return e}}enhanceSubtaskQuery(e,t){let r={...e},a=t.subtasks.filter(t=>"completed"===t.status&&t.result&&t.id!==e.id);if(0===a.length)return r;let s=[];for(let t of a){let r=t.result;if(t.description.toLowerCase().includes("earliest flight")){let t=this.extractFlightInfo(r);t&&(s.push(`${e.query} ${t}`),s.push(`${e.query} after ${t}`))}if(t.description.toLowerCase().includes("duration")){let t=this.extractDurationInfo(r);t&&s.push(`${e.query} ${t}`)}}return s.length>0&&(r.searchTerms=[...r.searchTerms||[],...s]),r}async executeComplexAutomationSearch(e,t,r){let a=this.getTargetSitesForQuery(r,e),s=[];for(let i of a)try{let a=this.buildTaskSpecificWorkflow(e,t,i,r),n=await this.browserlessService.executeComplexWorkflow(i,a);if(n&&n.data)return{success:!0,data:n.data};{let e=`Automation on ${i} returned no data`;s.push(e)}}catch(r){let e=r instanceof Error?r.message:"Unknown error",t=`Automation failed on ${i}: ${e}`;s.push(t)}try{let t=this.extractSearchQueryFromSubtask(e),r=await this.browserlessService.searchAndExtractUnblocked(t);if(r.success&&r.data)return{success:!0,data:r.data};s.push(`Final fallback search failed: ${r.error||"No data"}`)}catch(t){let e=t instanceof Error?t.message:"Unknown fallback error";s.push(`Final fallback search error: ${e}`)}return{success:!1,error:`All automation and fallback attempts failed. Errors: ${s.join("; ")}`}}getTargetSitesForQuery(e,t){(t.description||"").toLowerCase();let r=e.toLowerCase();return r.includes("flight")||r.includes("airline")||r.includes("departure")||r.includes("arrival")?["https://www.kayak.com","https://www.expedia.com","https://www.skyscanner.com","https://www.google.com/flights"]:r.includes("hotel")||r.includes("accommodation")||r.includes("booking")||r.includes("stay")?["https://www.booking.com","https://www.expedia.com","https://www.hotels.com","https://www.kayak.com"]:r.includes("restaurant")||r.includes("reservation")||r.includes("dining")||r.includes("table")?["https://www.opentable.com","https://www.yelp.com","https://www.resy.com"]:r.includes("buy")||r.includes("purchase")||r.includes("shop")||r.includes("product")?["https://www.amazon.com","https://www.google.com/shopping","https://www.ebay.com"]:r.includes("job")||r.includes("career")||r.includes("employment")||r.includes("hiring")?["https://www.linkedin.com/jobs","https://www.indeed.com","https://www.glassdoor.com"]:["https://www.google.com","https://www.bing.com","https://duckduckgo.com"]}buildTaskSpecificWorkflow(e,t,r,a){let s=a.toLowerCase();return s.includes("flight")||s.includes("airline")?this.buildFlightBookingWorkflow(e,t,r):this.buildAutomationWorkflow(e,r)}buildAnalysisPrompt(e,t){let r=new Date().toLocaleString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",timeZoneName:"short"}),a=t.map((e,t)=>`Source ${t+1} (${e.type}): ${e.description}
Query: ${e.query}
Data: ${JSON.stringify(e.data,null,2)}`).join("\n\n---\n\n");return`You are an expert information analyst. You must ONLY use the browsing data provided below to answer the query. Do NOT use your training data or general knowledge.

CURRENT DATE & TIME: ${r}
ORIGINAL QUERY: "${e}"

=== REAL-TIME BROWSING DATA ===
${a}
=== END BROWSING DATA ===

CRITICAL INSTRUCTIONS:
1. ONLY use information from the browsing data above - do NOT use your training data or general knowledge
2. If the browsing data doesn't contain sufficient information, clearly state this limitation
3. For time-sensitive queries (flights, schedules, prices), ONLY use data from the browsing results
4. When providing specific details (times, dates, prices), cite the exact source from the browsing data
5. If you find conflicting information in the browsing data, mention the discrepancies
6. Prioritize the most recent and relevant information from the browsing data
7. For queries about "today" or current information, emphasize that data is current as of ${r}

Please provide:
1. A comprehensive answer based ONLY on the browsing data
2. Key findings with specific details from the browsing results
3. Any conflicting information found in the browsing data
4. Confidence level based on the quality and completeness of the browsing data
5. Clear indication if more browsing is needed for complete information

IMPORTANT: Base your response ENTIRELY on the browsing data provided. Do NOT supplement with general knowledge or training data.

Format your response as a clear, well-structured analysis that directly addresses the user's query using only the browsing data.`}async callAIForPlanning(e,t){try{let r,a,s,i,n=this.getEffectiveModelId(t),o=[{role:"user",content:e}];switch(t.provider){case"openai":r="https://api.openai.com/v1/chat/completions",a={"Content-Type":"application/json",Authorization:`Bearer ${t.api_key}`},s={model:n,messages:o,temperature:.1,max_tokens:1500};break;case"google":r="https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",a={"Content-Type":"application/json",Authorization:`Bearer ${t.api_key}`},s={model:n,messages:o,temperature:.1,max_tokens:1500};break;case"anthropic":r="https://api.anthropic.com/v1/messages",a={"Content-Type":"application/json","x-api-key":t.api_key,"anthropic-version":"2023-06-01"},s={model:n,messages:o,temperature:.1,max_tokens:1500};break;case"openrouter":r="https://openrouter.ai/api/v1/chat/completions",a={"Content-Type":"application/json",Authorization:`Bearer ${t.api_key}`,"HTTP-Referer":"https://roukey.online","X-Title":"RouKey"},s={model:n,messages:o,temperature:.1,max_tokens:1500};break;default:throw Error(`Unsupported provider for planning: ${t.provider}`)}let l=await fetch(r,{method:"POST",headers:a,body:JSON.stringify(s),signal:AbortSignal.timeout(3e4)});if(!l.ok){let e=await l.text();throw Error(`API error: ${l.status} - ${e}`)}let c=await l.json();if(!(i="anthropic"===t.provider?c.content?.[0]?.text:c.choices?.[0]?.message?.content))throw Error("No content returned from AI model");return{success:!0,content:i}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async callAIForAnalysis(e,t){return this.callAIForPlanning(e,t)}getEffectiveModelId(e){if("openrouter"===e.provider.toLowerCase())return e.model;let t=e.model.split("/");return t.length>1?t[t.length-1]:e.model}parsePlanFromAI(e,t){try{let r=e.match(/\{[\s\S]*\}/);if(!r)throw Error("No JSON found in AI response");let a=JSON.parse(r[0]);if(!a.goal||!a.subtasks||!Array.isArray(a.subtasks))throw Error("Invalid plan structure from AI");let s=a.subtasks.map((e,r)=>({id:e.id||`ai_task_${Date.now()}_${r}`,type:e.type||"search",description:e.description||`Task ${r+1}`,query:e.query||t,status:"pending",priority:e.priority||5,attempts:0,maxAttempts:3,searchTerms:e.searchTerms||[],expectedInfo:e.expectedInfo||""}));return{goal:a.goal,subtasks:s}}catch(e){return{goal:`Find information about: ${t}`,subtasks:this.createFallbackPlan(t,"search")}}}async synthesizeFinalResult(e,t){try{let t=e.subtasks.find(e=>"analyze"===e.type&&"completed"===e.status&&e.result);if(t&&t.result?.analysis)return t.result.analysis;let r=e.subtasks.filter(e=>"completed"===e.status&&e.result);if(0===r.length)return`No information was successfully gathered for the query: "${e.originalQuery}"`;let a=`Based on browsing research for "${e.originalQuery}":

`;return r.forEach((e,t)=>{a+=`${t+1}. ${e.description}:
`,e.result?.results&&Array.isArray(e.result.results)?e.result.results.slice(0,3).forEach(e=>{a+=`   • ${e.title||"Result"}
`}):"string"==typeof e.result&&(a+=`   ${e.result.substring(0,200)}...
`),a+="\n"}),a}catch(t){return`Research completed for "${e.originalQuery}" but encountered errors in synthesis. Please check the individual results.`}}logPlan(e){e.subtasks.forEach((e,t)=>{e.searchTerms&&e.searchTerms.length})}async ensureBrowserSession(e,t){if(e.sessionInfo&&e.sessionInfo.reconnectUrl&&Date.now()-new Date(e.sessionInfo.lastUsed).getTime()<6e5)return e.sessionInfo.lastUsed=new Date,{sessionId:e.sessionInfo.sessionId,reconnectUrl:e.sessionInfo.reconnectUrl};let r=await this.browserlessService.createBrowsingSession(t||"https://www.google.com",{timeout:6e5,humanLike:!0});return e.sessionInfo={sessionId:r.sessionId,reconnectUrl:r.reconnectUrl,lastUsed:new Date,activeWorkflow:!0},r}requiresPersistentSession(e){let t=e.description.toLowerCase(),r=e.query.toLowerCase();return["form","booking","reservation","login","multi-step","workflow","navigation","complex","automation"].some(e=>t.includes(e)||r.includes(e))||["flight","airline","booking","search flight","travel"].some(e=>t.includes(e)||r.includes(e))}}class i{constructor(){this.browserlessService=a.A.getInstance(),this.smartBrowsingExecutor=s.getInstance()}static getInstance(){return i.instance||(i.instance=new i),i.instance}async executeBrowsing(e,t,r="search",a,s=!0,i){try{if(!t.browsing_enabled)return{success:!1,error:"Browsing is not enabled for this configuration"};if(!t.browsing_models||0===t.browsing_models.length)return{success:!1,error:"No browsing models configured"};if(s){let s=await this.smartBrowsingExecutor.executeSmartBrowsing(a||e,t,r,i);if(s.success)return{success:!0,content:s.content,modelUsed:t.browsing_models[0]?.model||"smart-browsing",providerUsed:t.browsing_models[0]?.provider||"smart-browsing",browsingData:s.plan}}return await this.executeSimpleBrowsing(e,t,r,a)}catch(t){let e=t instanceof Error?t.message:"Unknown error";return{success:!1,error:`Browsing execution failed: ${e}`}}}async executeSimpleBrowsing(e,t,r="search",a){let s=[...t.browsing_models].sort((e,t)=>e.order-t.order),i=null;for(let t of s)try{let s=await this.performWebBrowsing(a||e,r);if(!s.success)throw Error(s.error||"Browsing failed");let i=await this.processWithAI(e,s.data,t,r);if(i.success)return{success:!0,content:i.content,modelUsed:t.model,providerUsed:t.provider,browsingData:s.data};throw Error(i.error||"AI processing failed")}catch(e){i=e instanceof Error?e.message:"Unknown error";continue}return{success:!1,error:`All browsing models failed. Last error: ${i}`}}async performWebBrowsing(e,t){try{let r;switch(t){case"search":default:r=await this.browserlessService.searchAndExtractUnblocked(e);break;case"navigate":let a=e.match(/https?:\/\/[^\s]+/),s=a?a[0]:e;r=await this.browserlessService.navigateAndExtract(s);break;case"extract":let i=e.match(/https?:\/\/[^\s]+/)?.[0]||e;r=await this.browserlessService.navigateAndExtract(i)}if(r&&r.data)return{success:!0,data:r.data};return{success:!1,error:"No data returned from browsing"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async processWithAI(e,t,r,a){try{let s=this.buildProcessingPrompt(e,t,a),i=await this.callAIProvider(s,r);if(i&&i.content)return{success:!0,content:i.content};return{success:!1,error:"No content returned from AI model"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}buildProcessingPrompt(e,t,r){let a=JSON.stringify(t,null,2),s=new Date().toLocaleString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",timeZoneName:"short"});return`You are an AI assistant that processes REAL-TIME web browsing results to answer user queries. You must ONLY use the browsing data provided below - do NOT use your training data or general knowledge.

CURRENT DATE & TIME: ${s}
USER QUERY: "${e}"
BROWSING TYPE: ${r}

=== REAL-TIME WEB BROWSING RESULTS ===
${a}
=== END BROWSING RESULTS ===

CRITICAL INSTRUCTIONS:
1. ONLY use information from the browsing results above - do NOT use your training data
2. If the browsing results don't contain the information needed, clearly state "The browsing results do not contain sufficient information to answer this query"
3. When providing specific details (times, dates, prices, schedules), ONLY use data from the browsing results
4. Include specific details, numbers, dates, and facts ONLY from the browsing results
5. Cite sources when possible (URLs, website names from the browsing data)
6. If you find conflicting information in the browsing results, mention the discrepancies
7. Prioritize the most recent and relevant information from the browsing results
8. For time-sensitive queries, emphasize that the information is current as of ${s}

IMPORTANT: Do NOT make assumptions or use general knowledge. Base your response ENTIRELY on the browsing data provided above.

Please provide a response based ONLY on the browsing results:`}getEffectiveModelId(e){if("openrouter"===e.provider.toLowerCase())return e.model;let t=e.model.split("/");return t.length>1?t[t.length-1]:e.model}async callAIProvider(e,t){try{let r,a,s,i,n=[{role:"user",content:e}],o=this.getEffectiveModelId(t);switch(t.provider){case"openai":r="https://api.openai.com/v1/chat/completions",a={"Content-Type":"application/json",Authorization:`Bearer ${t.api_key}`},s={model:o,messages:n,temperature:t.temperature||.2,max_tokens:2e3};break;case"anthropic":r="https://api.anthropic.com/v1/messages",a={"Content-Type":"application/json","x-api-key":t.api_key,"anthropic-version":"2023-06-01"},s={model:o,messages:n,temperature:t.temperature||.2,max_tokens:2e3};break;case"google":r="https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",a={"Content-Type":"application/json",Authorization:`Bearer ${t.api_key}`},s={model:o,messages:n,temperature:t.temperature||.2,max_tokens:2e3};break;case"openrouter":r="https://openrouter.ai/api/v1/chat/completions",a={"Content-Type":"application/json",Authorization:`Bearer ${t.api_key}`,"HTTP-Referer":"https://roukey.online","X-Title":"RouKey"},s={model:o,messages:n,temperature:t.temperature||.2,max_tokens:2e3};break;default:throw Error(`Unsupported provider: ${t.provider}`)}let l=await fetch(r,{method:"POST",headers:a,body:JSON.stringify(s),signal:AbortSignal.timeout(3e4)});if(!l.ok){let e=await l.text();throw Error(`API error: ${l.status} - ${e}`)}let c=await l.json();if(!(i="anthropic"===t.provider?c.content?.[0]?.text:c.choices?.[0]?.message?.content)||0===i.trim().length)return{error:"No content returned from AI model - empty response"};return{content:i}}catch(e){return{error:e instanceof Error?e.message:"Unknown error"}}}}}};