{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "wN9hsC9ZYdN4LpR1YsIP3", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "1fd1a66649cb8ad7090e7b07c5677383", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bf4f8311f21d14063eaa842e6b1126e6f2a9a992d301fa32fae6dd7ae0a3c7ab", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a4d4c55535e452c9fb1499567aec48d3a6cb62e67aa1de7edf82b66a14cec034"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "wN9hsC9ZYdN4LpR1YsIP3", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "1fd1a66649cb8ad7090e7b07c5677383", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bf4f8311f21d14063eaa842e6b1126e6f2a9a992d301fa32fae6dd7ae0a3c7ab", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a4d4c55535e452c9fb1499567aec48d3a6cb62e67aa1de7edf82b66a14cec034"}}}, "sortedMiddleware": ["/"]}