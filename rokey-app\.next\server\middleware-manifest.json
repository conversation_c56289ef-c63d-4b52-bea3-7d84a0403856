{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "9i-w_xG6LVivn03N7eoMR", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "f6143c05faf41a9d1fb3ffa2bb2207cb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b451411e904b8bd74777d72aba64dbd510218261d4e4a61890d89316f6ee166b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2c7fc08b7835ff585712cdb80bc8dd8912b246143b0aa78033fc12acf57addff"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "9i-w_xG6LVivn03N7eoMR", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "f6143c05faf41a9d1fb3ffa2bb2207cb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b451411e904b8bd74777d72aba64dbd510218261d4e4a61890d89316f6ee166b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2c7fc08b7835ff585712cdb80bc8dd8912b246143b0aa78033fc12acf57addff"}}}, "sortedMiddleware": ["/"]}