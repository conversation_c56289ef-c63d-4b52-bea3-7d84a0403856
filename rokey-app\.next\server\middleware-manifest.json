{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Tpbk95NR6qFp7F64B5AwQ", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "4ab5955689a56559d2983b84bd85d539", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "181dd834048931f3225906cfa36909ad70a8889606313a0dbb1022f9d5bf3ebd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7a80e8dbfa7e30158b24483d4057eba3285d9b4cfb1a9cd6ee37bee69896c9d1"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Tpbk95NR6qFp7F64B5AwQ", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "4ab5955689a56559d2983b84bd85d539", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "181dd834048931f3225906cfa36909ad70a8889606313a0dbb1022f9d5bf3ebd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7a80e8dbfa7e30158b24483d4057eba3285d9b4cfb1a9cd6ee37bee69896c9d1"}}}, "sortedMiddleware": ["/"]}